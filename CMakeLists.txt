cmake_minimum_required(VERSION 3.31)
project(freeradius_google_sso C)

set(CMAKE_C_STANDARD 11)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(CURL REQUIRED libcurl)
pkg_check_modules(JSON REQUIRED json-c)
pkg_check_modules(OPENSSL REQUIRED openssl)

# Include directories
include_directories(${CURL_INCLUDE_DIRS})
include_directories(${JSON_INCLUDE_DIRS})
include_directories(${OPENSSL_INCLUDE_DIRS})

# Add the FreeRADIUS module
add_library(rlm_google_sso SHARED
    src/rlm_google_sso.c
)

# Link libraries
target_link_libraries(rlm_google_sso
    ${CURL_LIBRARIES}
    ${JSON_LIBRARIES}
    ${OPENSSL_LIBRARIES}
)

# Compiler flags
target_compile_options(rlm_google_sso PRIVATE ${CURL_CFLAGS_OTHER})
target_compile_options(rlm_google_sso PRIVATE ${JSON_CFLAGS_OTHER})
target_compile_options(rlm_google_sso PRIVATE ${OPENSSL_CFLAGS_OTHER})
