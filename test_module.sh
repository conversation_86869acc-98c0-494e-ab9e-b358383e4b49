#!/bin/bash

#
# test_module.sh - Test script for FreeRADIUS Google SSO Module
#
# This script provides various tests to validate the module installation
# and configuration
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FREERADIUS_CONFIG_DIR="/etc/freeradius"
RADIUS_SERVER="localhost"
RADIUS_SECRET="testing123"
TEST_USER="<EMAIL>"
TEST_PASSWORD="testpassword"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test module loading
test_module_loading() {
    print_status "Testing module loading..."
    
    # Check if module file exists
    if [[ -f "/usr/lib/freeradius/librlm_google_sso.so" ]]; then
        print_success "Module library found"
    else
        print_error "Module library not found"
        return 1
    fi
    
    # Check if configuration exists
    if [[ -f "$FREERADIUS_CONFIG_DIR/mods-enabled/google_sso" ]]; then
        print_success "Module configuration found"
    else
        print_error "Module configuration not found"
        return 1
    fi
    
    # Check if dictionary is included
    if grep -q "dictionary.google_sso" "$FREERADIUS_CONFIG_DIR/dictionary"; then
        print_success "Dictionary included"
    else
        print_error "Dictionary not included"
        return 1
    fi
    
    return 0
}

# Function to test FreeRADIUS configuration
test_freeradius_config() {
    print_status "Testing FreeRADIUS configuration..."
    
    # Test configuration syntax
    if sudo freeradius -C &> /dev/null; then
        print_success "FreeRADIUS configuration syntax is valid"
    else
        print_error "FreeRADIUS configuration has syntax errors"
        print_warning "Run 'sudo freeradius -C' for details"
        return 1
    fi
    
    return 0
}

# Function to test module instantiation
test_module_instantiation() {
    print_status "Testing module instantiation..."
    
    # Start FreeRADIUS in debug mode and check for module loading
    local output
    output=$(timeout 10s sudo freeradius -X 2>&1 || true)
    
    if echo "$output" | grep -q "rlm_google_sso.*Instantiation complete"; then
        print_success "Module instantiated successfully"
    elif echo "$output" | grep -q "rlm_google_sso"; then
        print_warning "Module loaded but may have configuration issues"
        echo "$output" | grep "rlm_google_sso"
    else
        print_error "Module not found in FreeRADIUS output"
        return 1
    fi
    
    return 0
}

# Function to test basic authentication
test_basic_auth() {
    print_status "Testing basic authentication..."
    
    # Check if radtest is available
    if ! command -v radtest &> /dev/null; then
        print_warning "radtest not available, skipping basic auth test"
        return 0
    fi
    
    # Test authentication (this will likely fail without proper Google setup)
    local result
    if result=$(radtest "$TEST_USER" "$TEST_PASSWORD" "$RADIUS_SERVER" 0 "$RADIUS_SECRET" 2>&1); then
        print_success "Authentication test passed"
        echo "$result"
    else
        print_warning "Authentication test failed (expected without Google configuration)"
        echo "$result" | head -5
    fi
    
    return 0
}

# Function to test EAP-TTLS configuration
test_eap_ttls() {
    print_status "Testing EAP-TTLS configuration..."
    
    # Check if eapol_test is available
    if ! command -v eapol_test &> /dev/null; then
        print_warning "eapol_test not available, skipping EAP test"
        return 0
    fi
    
    # Create temporary test configuration
    local test_config="/tmp/eapol_test.conf"
    cat > "$test_config" << EOF
network={
    key_mgmt=IEEE8021X
    eap=TTLS
    identity="$TEST_USER"
    password="$TEST_PASSWORD"
    phase2="auth=PAP"
}
EOF
    
    # Test EAP-TTLS authentication
    local result
    if result=$(timeout 30s eapol_test -c "$test_config" -s "$RADIUS_SECRET" -a "$RADIUS_SERVER" 2>&1 || true); then
        if echo "$result" | grep -q "CTRL-EVENT-EAP-SUCCESS"; then
            print_success "EAP-TTLS test passed"
        else
            print_warning "EAP-TTLS test failed (expected without proper configuration)"
        fi
    else
        print_warning "EAP-TTLS test timed out or failed"
    fi
    
    rm -f "$test_config"
    return 0
}

# Function to test Google API connectivity
test_google_api() {
    print_status "Testing Google API connectivity..."
    
    # Test basic connectivity to Google APIs
    if curl -s --connect-timeout 10 "https://www.googleapis.com" > /dev/null; then
        print_success "Google APIs are reachable"
    else
        print_error "Cannot reach Google APIs"
        return 1
    fi
    
    # Test OAuth2 endpoint
    if curl -s --connect-timeout 10 "https://oauth2.googleapis.com/token" > /dev/null; then
        print_success "Google OAuth2 endpoint is reachable"
    else
        print_error "Cannot reach Google OAuth2 endpoint"
        return 1
    fi
    
    return 0
}

# Function to validate configuration file
test_config_file() {
    print_status "Validating configuration file..."
    
    local config_file="$FREERADIUS_CONFIG_DIR/mods-enabled/google_sso"
    
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found"
        return 1
    fi
    
    # Check for required configuration parameters
    local required_params=(
        "service_account_email"
        "service_account_key"
        "domain"
        "admin_email"
    )
    
    for param in "${required_params[@]}"; do
        if grep -q "^[[:space:]]*$param[[:space:]]*=" "$config_file"; then
            print_success "Found required parameter: $param"
        else
            print_warning "Missing or commented parameter: $param"
        fi
    done
    
    return 0
}

# Function to check dependencies
test_dependencies() {
    print_status "Checking dependencies..."
    
    local deps=("curl" "openssl" "pkg-config")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" &> /dev/null; then
            print_success "Found dependency: $dep"
        else
            print_error "Missing dependency: $dep"
            missing_deps+=("$dep")
        fi
    done
    
    # Check for development libraries
    local lib_checks=(
        "pkg-config --exists libcurl"
        "pkg-config --exists json-c"
        "pkg-config --exists openssl"
    )
    
    for check in "${lib_checks[@]}"; do
        if eval "$check" &> /dev/null; then
            local lib_name=$(echo "$check" | awk '{print $3}')
            print_success "Found library: $lib_name"
        else
            local lib_name=$(echo "$check" | awk '{print $3}')
            print_error "Missing library: $lib_name"
        fi
    done
    
    if [[ ${#missing_deps[@]} -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests..."
    echo
    
    local tests=(
        "test_dependencies"
        "test_module_loading"
        "test_config_file"
        "test_freeradius_config"
        "test_module_instantiation"
        "test_google_api"
        "test_basic_auth"
        "test_eap_ttls"
    )
    
    local passed=0
    local failed=0
    local warnings=0
    
    for test in "${tests[@]}"; do
        echo
        if $test; then
            ((passed++))
        else
            ((failed++))
        fi
    done
    
    echo
    print_status "Test Summary:"
    echo "  Passed: $passed"
    echo "  Failed: $failed"
    
    if [[ $failed -eq 0 ]]; then
        print_success "All tests passed!"
        return 0
    else
        print_warning "Some tests failed. Check the output above for details."
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [test_name]"
    echo
    echo "Available tests:"
    echo "  all              - Run all tests (default)"
    echo "  dependencies     - Check system dependencies"
    echo "  loading          - Test module loading"
    echo "  config           - Validate configuration"
    echo "  freeradius       - Test FreeRADIUS configuration"
    echo "  instantiation    - Test module instantiation"
    echo "  google-api       - Test Google API connectivity"
    echo "  basic-auth       - Test basic authentication"
    echo "  eap-ttls         - Test EAP-TTLS authentication"
    echo
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 dependencies       # Check only dependencies"
    echo "  $0 config             # Validate only configuration"
}

# Main function
main() {
    case "${1:-all}" in
        all)
            run_all_tests
            ;;
        dependencies)
            test_dependencies
            ;;
        loading)
            test_module_loading
            ;;
        config)
            test_config_file
            ;;
        freeradius)
            test_freeradius_config
            ;;
        instantiation)
            test_module_instantiation
            ;;
        google-api)
            test_google_api
            ;;
        basic-auth)
            test_basic_auth
            ;;
        eap-ttls)
            test_eap_ttls
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown test: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
