#
# dictionary.google_sso
#
# Custom RADIUS attributes for Google SSO authentication module
#
# Copyright (C) 2025 FreeRADIUS Google SSO Module
#

VENDOR		Google-SSO			65001

BEGIN-VEN<PERSON>OR	Google-SSO

# User Information Attributes
ATTRIBUTE	Google-User-ID			1	string
ATTRIBUTE	Google-User-Email		2	string
ATTRIBUTE	Google-User-Name		3	string
ATTRIBUTE	Google-User-Groups		4	string
ATTRIBUTE	Google-User-Domain		5	string
ATTRIBUTE	Google-User-OrgUnit		6	string

# Authentication Attributes
ATTRIBUTE	Google-Auth-Method		10	integer
ATTRIBUTE	Google-Auth-Timestamp		11	date
ATTRIBUTE	Google-Auth-Source		12	string
ATTRIBUTE	Google-Session-ID		13	string

# Certificate Attributes
ATTRIBUTE	Google-Certificate-DN		20	string
ATTRIBUTE	Google-Certificate-Serial	21	string
ATTRIBUTE	Google-Certificate-Issuer	22	string
ATTRIBUTE	Google-Certificate-Valid-From	23	date
ATTRIBUTE	Google-Certificate-Valid-To	24	date

# Device Attributes
AT<PERSON><PERSON><PERSON><PERSON>	Google-Device-ID		30	string
ATTRIBUTE	Google-Device-Type		31	string
ATTRIBUTE	Google-Device-OS		32	string
ATTRIBUTE	Google-Device-Managed		33	integer

# Policy Attributes
ATTRIBUTE	Google-Access-Level		40	integer
ATTRIBUTE	Google-VLAN-ID			41	integer
ATTRIBUTE	Google-Bandwidth-Limit		42	integer
ATTRIBUTE	Google-Session-Timeout		43	integer

# Security Attributes
ATTRIBUTE	Google-Risk-Score		50	integer
ATTRIBUTE	Google-MFA-Required		51	integer
ATTRIBUTE	Google-MFA-Method		52	string
ATTRIBUTE	Google-Location-Verified	53	integer

END-VENDOR	Google-SSO

#
# Authentication Method Values
#
VALUE	Google-Auth-Method		Password		1
VALUE	Google-Auth-Method		Certificate		2
VALUE	Google-Auth-Method		Cached			3
VALUE	Google-Auth-Method		MFA			4
VALUE	Google-Auth-Method		SSO			5

#
# Device Type Values
#
VALUE	Google-Device-Type		Unknown			0
VALUE	Google-Device-Type		Desktop			1
VALUE	Google-Device-Type		Laptop			2
VALUE	Google-Device-Type		Mobile			3
VALUE	Google-Device-Type		Tablet			4
VALUE	Google-Device-Type		Chromebook		5

#
# Access Level Values
#
VALUE	Google-Access-Level		Denied			0
VALUE	Google-Access-Level		Guest			1
VALUE	Google-Access-Level		User			2
VALUE	Google-Access-Level		Staff			3
VALUE	Google-Access-Level		Admin			4

#
# Boolean Values
#
VALUE	Google-Device-Managed		No			0
VALUE	Google-Device-Managed		Yes			1

VALUE	Google-MFA-Required		No			0
VALUE	Google-MFA-Required		Yes			1

VALUE	Google-Location-Verified	No			0
VALUE	Google-Location-Verified	Yes			1
