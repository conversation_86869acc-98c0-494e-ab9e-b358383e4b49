#ifndef RLM_GOOGLE_SSO_H
#define RLM_GOOGLE_SSO_H

#include <freeradius-devel/radiusd.h>
#include <freeradius-devel/modules.h>
#include <freeradius-devel/rad_assert.h>
#include <freeradius-devel/libradius.h>

#include <curl/curl.h>
#include <json-c/json.h>
#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/x509.h>
#include <openssl/rand.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Define a structure for our module configuration.
 */
typedef struct rlm_google_sso_t {
    char const *name;                    /* Module instance name */
    
    /* Google Workspace Configuration */
    char const *service_account_email;   /* Service account email */
    char const *service_account_key;     /* Path to service account private key */
    char const *domain;                  /* Google Workspace domain */
    char const *admin_email;             /* Admin email for domain-wide delegation */
    
    /* API Configuration */
    char const *api_base_url;            /* Google API base URL */
    uint32_t api_timeout;                /* API request timeout in seconds */
    uint32_t token_cache_ttl;            /* Access token cache TTL in seconds */
    
    /* Authentication Configuration */
    bool enable_certificate_auth;        /* Enable EAP-TLS certificate authentication */
    bool enable_password_fallback;       /* Enable password fallback authentication */
    char const *certificate_template;    /* Certificate template for user certs */
    uint32_t certificate_validity_days;  /* Certificate validity period */
    
    /* Security Configuration */
    uint32_t max_auth_attempts;          /* Maximum authentication attempts */
    uint32_t lockout_duration;           /* Account lockout duration in seconds */
    bool require_group_membership;       /* Require specific group membership */
    char const *required_groups;         /* Comma-separated list of required groups */
    
    /* Caching Configuration */
    uint32_t user_cache_ttl;             /* User info cache TTL in seconds */
    uint32_t group_cache_ttl;            /* Group membership cache TTL in seconds */
    bool enable_offline_auth;            /* Enable offline authentication */
    
    /* Logging Configuration */
    bool debug_mode;                     /* Enable debug logging */
    char const *log_file;                /* Optional log file path */
    
    /* Runtime data */
    CURL *curl_handle;                   /* Shared CURL handle */
    char *access_token;                  /* Cached access token */
    time_t token_expires_at;             /* Token expiration timestamp */
    EVP_PKEY *ca_private_key;            /* CA private key for certificate signing */
    X509 *ca_certificate;                /* CA certificate */
    
    /* Thread safety */
    pthread_mutex_t token_mutex;         /* Mutex for token operations */
    pthread_mutex_t cache_mutex;         /* Mutex for cache operations */
    
} rlm_google_sso_t;

/*
 * Structure for HTTP response data
 */
typedef struct http_response_t {
    char *data;
    size_t size;
    long response_code;
} http_response_t;

/*
 * Structure for user information
 */
typedef struct google_user_t {
    char *email;
    char *name;
    char *id;
    char **groups;
    size_t group_count;
    bool is_active;
    time_t cached_at;
} google_user_t;

/*
 * Structure for cached authentication data
 */
typedef struct auth_cache_entry_t {
    char *username;
    char *password_hash;
    time_t last_auth;
    uint32_t failed_attempts;
    time_t locked_until;
    google_user_t *user_info;
} auth_cache_entry_t;

/*
 * Custom RADIUS attributes (defined in dictionary.google_sso)
 */
#define PW_GOOGLE_USER_ID           3000
#define PW_GOOGLE_USER_EMAIL        3001
#define PW_GOOGLE_USER_NAME         3002
#define PW_GOOGLE_USER_GROUPS       3003
#define PW_GOOGLE_AUTH_METHOD       3004
#define PW_GOOGLE_CERTIFICATE_DN    3005
#define PW_GOOGLE_DEVICE_ID         3006

/*
 * Authentication methods
 */
typedef enum {
    GOOGLE_AUTH_PASSWORD = 1,
    GOOGLE_AUTH_CERTIFICATE = 2,
    GOOGLE_AUTH_CACHED = 3
} google_auth_method_t;

/*
 * Function prototypes
 */

/* Module lifecycle functions */
static int mod_bootstrap(CONF_SECTION *conf, void *instance);
static int mod_instantiate(CONF_SECTION *conf, void *instance);
static int mod_detach(void *instance);

/* Request processing functions */
static rlm_rcode_t mod_authorize(void *instance, REQUEST *request);
static rlm_rcode_t mod_authenticate(void *instance, REQUEST *request);
static rlm_rcode_t mod_post_auth(void *instance, REQUEST *request);

/* Google API functions */
static int google_get_access_token(rlm_google_sso_t *inst);
static int google_validate_user(rlm_google_sso_t *inst, const char *username, const char *password);
static google_user_t *google_get_user_info(rlm_google_sso_t *inst, const char *username);
static char **google_get_user_groups(rlm_google_sso_t *inst, const char *user_id);

/* Certificate functions */
static X509 *google_generate_user_certificate(rlm_google_sso_t *inst, google_user_t *user);
static int google_verify_certificate(rlm_google_sso_t *inst, X509 *cert);

/* Utility functions */
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, http_response_t *response);
static char *create_jwt_token(rlm_google_sso_t *inst, const char *scope);
static char *hash_password(const char *password, const char *salt);
static bool verify_password_hash(const char *password, const char *hash);
static bool check_group_membership(rlm_google_sso_t *inst, google_user_t *user);

/* Cache management functions */
static auth_cache_entry_t *cache_get_user(rlm_google_sso_t *inst, const char *username);
static int cache_store_user(rlm_google_sso_t *inst, const char *username, google_user_t *user);
static void cache_cleanup_expired(rlm_google_sso_t *inst);

/* Memory management functions */
static void google_user_free(google_user_t *user);
static void auth_cache_entry_free(auth_cache_entry_t *entry);
static void http_response_free(http_response_t *response);

#ifdef __cplusplus
}
#endif

#endif /* RLM_GOOGLE_SSO_H */
