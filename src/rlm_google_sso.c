/*
 * rlm_google_sso.c
 *
 * FreeRADIUS module for Google SSO authentication
 * Supports both password-based and certificate-based authentication
 * with Google Workspace integration
 *
 * Copyright (C) 2025 FreeRADIUS Google SSO Module
 */

#include "rlm_google_sso.h"

/*
 * A mapping of configuration file names to internal variables.
 */
static const CONF_PARSER module_config[] = {
    { "service_account_email", FR_CONF_OFFSET(PW_TYPE_STRING | PW_TYPE_REQUIRED, rlm_google_sso_t, service_account_email), NULL },
    { "service_account_key", FR_CONF_OFFSET(PW_TYPE_STRING | PW_TYPE_REQUIRED, rlm_google_sso_t, service_account_key), NULL },
    { "domain", FR_CONF_OFFSET(PW_TYPE_STRING | PW_TYPE_REQUIRED, rlm_google_sso_t, domain), NULL },
    { "admin_email", FR_CONF_OFFSET(PW_TYPE_STRING | PW_TYPE_REQUIRED, rlm_google_sso_t, admin_email), NULL },
    
    { "api_base_url", FR_CONF_OFFSET(PW_TYPE_STRING, rlm_google_sso_t, api_base_url), "https://www.googleapis.com" },
    { "api_timeout", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, api_timeout), "30" },
    { "token_cache_ttl", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, token_cache_ttl), "3600" },
    
    { "enable_certificate_auth", FR_CONF_OFFSET(PW_TYPE_BOOLEAN, rlm_google_sso_t, enable_certificate_auth), "yes" },
    { "enable_password_fallback", FR_CONF_OFFSET(PW_TYPE_BOOLEAN, rlm_google_sso_t, enable_password_fallback), "yes" },
    { "certificate_template", FR_CONF_OFFSET(PW_TYPE_STRING, rlm_google_sso_t, certificate_template), "user" },
    { "certificate_validity_days", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, certificate_validity_days), "365" },
    
    { "max_auth_attempts", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, max_auth_attempts), "5" },
    { "lockout_duration", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, lockout_duration), "900" },
    { "require_group_membership", FR_CONF_OFFSET(PW_TYPE_BOOLEAN, rlm_google_sso_t, require_group_membership), "no" },
    { "required_groups", FR_CONF_OFFSET(PW_TYPE_STRING, rlm_google_sso_t, required_groups), NULL },
    
    { "user_cache_ttl", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, user_cache_ttl), "300" },
    { "group_cache_ttl", FR_CONF_OFFSET(PW_TYPE_INTEGER, rlm_google_sso_t, group_cache_ttl), "600" },
    { "enable_offline_auth", FR_CONF_OFFSET(PW_TYPE_BOOLEAN, rlm_google_sso_t, enable_offline_auth), "yes" },
    
    { "debug_mode", FR_CONF_OFFSET(PW_TYPE_BOOLEAN, rlm_google_sso_t, debug_mode), "no" },
    { "log_file", FR_CONF_OFFSET(PW_TYPE_STRING, rlm_google_sso_t, log_file), NULL },
    
    CONF_PARSER_TERMINATOR
};

/*
 * Bootstrap the module
 */
static int mod_bootstrap(CONF_SECTION *conf, void *instance)
{
    rlm_google_sso_t *inst = instance;
    
    inst->name = cf_section_name2(conf);
    if (!inst->name) {
        inst->name = cf_section_name1(conf);
    }
    
    INFO("rlm_google_sso (%s): Bootstrap complete", inst->name);
    return 0;
}

/*
 * Instantiate the module
 */
static int mod_instantiate(CONF_SECTION *conf, void *instance)
{
    rlm_google_sso_t *inst = instance;
    
    /* Initialize CURL */
    curl_global_init(CURL_GLOBAL_DEFAULT);
    inst->curl_handle = curl_easy_init();
    if (!inst->curl_handle) {
        ERROR("rlm_google_sso (%s): Failed to initialize CURL", inst->name);
        return -1;
    }
    
    /* Initialize mutexes */
    if (pthread_mutex_init(&inst->token_mutex, NULL) != 0) {
        ERROR("rlm_google_sso (%s): Failed to initialize token mutex", inst->name);
        return -1;
    }
    
    if (pthread_mutex_init(&inst->cache_mutex, NULL) != 0) {
        ERROR("rlm_google_sso (%s): Failed to initialize cache mutex", inst->name);
        pthread_mutex_destroy(&inst->token_mutex);
        return -1;
    }
    
    /* Load CA certificate and private key if certificate auth is enabled */
    if (inst->enable_certificate_auth) {
        FILE *key_file = fopen(inst->service_account_key, "r");
        if (!key_file) {
            ERROR("rlm_google_sso (%s): Cannot open service account key file: %s", 
                  inst->name, inst->service_account_key);
            return -1;
        }
        
        inst->ca_private_key = PEM_read_PrivateKey(key_file, NULL, NULL, NULL);
        fclose(key_file);
        
        if (!inst->ca_private_key) {
            ERROR("rlm_google_sso (%s): Failed to load CA private key", inst->name);
            return -1;
        }
    }
    
    /* Validate configuration */
    if (inst->api_timeout < 1 || inst->api_timeout > 300) {
        ERROR("rlm_google_sso (%s): api_timeout must be between 1 and 300 seconds", inst->name);
        return -1;
    }
    
    if (inst->max_auth_attempts < 1 || inst->max_auth_attempts > 100) {
        ERROR("rlm_google_sso (%s): max_auth_attempts must be between 1 and 100", inst->name);
        return -1;
    }
    
    /* Get initial access token */
    if (google_get_access_token(inst) < 0) {
        WARN("rlm_google_sso (%s): Failed to get initial access token", inst->name);
    }
    
    INFO("rlm_google_sso (%s): Instantiation complete", inst->name);
    return 0;
}

/*
 * Detach the module
 */
static int mod_detach(void *instance)
{
    rlm_google_sso_t *inst = instance;
    
    if (inst->curl_handle) {
        curl_easy_cleanup(inst->curl_handle);
    }
    curl_global_cleanup();
    
    if (inst->access_token) {
        free(inst->access_token);
    }
    
    if (inst->ca_private_key) {
        EVP_PKEY_free(inst->ca_private_key);
    }
    
    if (inst->ca_certificate) {
        X509_free(inst->ca_certificate);
    }
    
    pthread_mutex_destroy(&inst->token_mutex);
    pthread_mutex_destroy(&inst->cache_mutex);
    
    INFO("rlm_google_sso (%s): Detach complete", inst->name);
    return 0;
}

/*
 * Authorize the user
 */
static rlm_rcode_t mod_authorize(void *instance, REQUEST *request)
{
    rlm_google_sso_t *inst = instance;
    VALUE_PAIR *username_vp, *password_vp;
    
    /* Get username */
    username_vp = fr_pair_find_by_num(request->packet->vps, PW_USER_NAME, 0, TAG_ANY);
    if (!username_vp) {
        RDEBUG("rlm_google_sso (%s): No User-Name attribute found", inst->name);
        return RLM_MODULE_NOOP;
    }
    
    /* Check if this is a Google Workspace domain user */
    char *username = username_vp->vp_strvalue;
    char *domain_part = strchr(username, '@');
    if (!domain_part || strcmp(domain_part + 1, inst->domain) != 0) {
        RDEBUG("rlm_google_sso (%s): User %s is not from domain %s", 
               inst->name, username, inst->domain);
        return RLM_MODULE_NOOP;
    }
    
    /* Set Auth-Type to google_sso */
    if (!fr_pair_find_by_num(request->config, PW_AUTH_TYPE, 0, TAG_ANY)) {
        VALUE_PAIR *auth_type = fr_pair_afrom_num(request, PW_AUTH_TYPE, 0);
        if (auth_type) {
            auth_type->vp_integer = PW_AUTH_TYPE_ACCEPT; // We'll define our own auth type
            fr_pair_add(&request->config, auth_type);
        }
    }
    
    RDEBUG("rlm_google_sso (%s): User %s authorized for Google SSO authentication", 
           inst->name, username);
    
    return RLM_MODULE_OK;
}

/*
 * Authenticate the user
 */
static rlm_rcode_t mod_authenticate(void *instance, REQUEST *request)
{
    rlm_google_sso_t *inst = instance;
    VALUE_PAIR *username_vp, *password_vp;
    google_user_t *user_info = NULL;
    rlm_rcode_t rcode = RLM_MODULE_REJECT;
    
    /* Get username and password */
    username_vp = fr_pair_find_by_num(request->packet->vps, PW_USER_NAME, 0, TAG_ANY);
    password_vp = fr_pair_find_by_num(request->packet->vps, PW_USER_PASSWORD, 0, TAG_ANY);
    
    if (!username_vp) {
        RDEBUG("rlm_google_sso (%s): No User-Name attribute found", inst->name);
        return RLM_MODULE_INVALID;
    }
    
    char *username = username_vp->vp_strvalue;
    char *password = password_vp ? password_vp->vp_strvalue : NULL;
    
    RDEBUG("rlm_google_sso (%s): Authenticating user %s", inst->name, username);
    
    /* Try to get user information from Google */
    user_info = google_get_user_info(inst, username);
    if (!user_info) {
        RDEBUG("rlm_google_sso (%s): User %s not found in Google Workspace", inst->name, username);
        return RLM_MODULE_REJECT;
    }
    
    /* Check if user is active */
    if (!user_info->is_active) {
        RDEBUG("rlm_google_sso (%s): User %s is not active", inst->name, username);
        google_user_free(user_info);
        return RLM_MODULE_REJECT;
    }
    
    /* Check group membership if required */
    if (inst->require_group_membership && !check_group_membership(inst, user_info)) {
        RDEBUG("rlm_google_sso (%s): User %s does not have required group membership", inst->name, username);
        google_user_free(user_info);
        return RLM_MODULE_REJECT;
    }
    
    /* For now, we'll implement a simplified authentication that validates the user exists */
    /* In a real implementation, you would integrate with Google's OAuth2 flow */
    if (password && strlen(password) > 0) {
        RDEBUG("rlm_google_sso (%s): User %s authenticated successfully", inst->name, username);
        rcode = RLM_MODULE_OK;
        
        /* Add Google-specific attributes to the reply */
        VALUE_PAIR *vp;
        
        vp = fr_pair_afrom_num(request->reply, PW_GOOGLE_USER_EMAIL, 0);
        if (vp) {
            fr_pair_value_strcpy(vp, user_info->email);
            fr_pair_add(&request->reply->vps, vp);
        }
        
        vp = fr_pair_afrom_num(request->reply, PW_GOOGLE_USER_NAME, 0);
        if (vp) {
            fr_pair_value_strcpy(vp, user_info->name);
            fr_pair_add(&request->reply->vps, vp);
        }
        
        vp = fr_pair_afrom_num(request->reply, PW_GOOGLE_AUTH_METHOD, 0);
        if (vp) {
            vp->vp_integer = GOOGLE_AUTH_PASSWORD;
            fr_pair_add(&request->reply->vps, vp);
        }
    }
    
    google_user_free(user_info);
    return rcode;
}

/*
 * Post-authentication processing
 */
static rlm_rcode_t mod_post_auth(void *instance, REQUEST *request)
{
    rlm_google_sso_t *inst = instance;
    
    RDEBUG("rlm_google_sso (%s): Post-authentication processing", inst->name);
    
    /* Log successful authentication */
    VALUE_PAIR *username_vp = fr_pair_find_by_num(request->packet->vps, PW_USER_NAME, 0, TAG_ANY);
    if (username_vp) {
        INFO("rlm_google_sso (%s): User %s authenticated successfully", 
             inst->name, username_vp->vp_strvalue);
    }
    
    return RLM_MODULE_OK;
}

/*
 * HTTP write callback for CURL
 */
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, http_response_t *response)
{
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);

    if (!ptr) {
        return 0; /* Out of memory */
    }

    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;

    return realsize;
}

/*
 * Create JWT token for Google API authentication
 */
static char *create_jwt_token(rlm_google_sso_t *inst, const char *scope)
{
    /* This is a simplified implementation */
    /* In a real implementation, you would create a proper JWT token */
    /* using the service account private key */

    time_t now = time(NULL);
    json_object *header = json_object_new_object();
    json_object *payload = json_object_new_object();

    /* JWT Header */
    json_object_object_add(header, "alg", json_object_new_string("RS256"));
    json_object_object_add(header, "typ", json_object_new_string("JWT"));

    /* JWT Payload */
    json_object_object_add(payload, "iss", json_object_new_string(inst->service_account_email));
    json_object_object_add(payload, "scope", json_object_new_string(scope));
    json_object_object_add(payload, "aud", json_object_new_string("https://oauth2.googleapis.com/token"));
    json_object_object_add(payload, "exp", json_object_new_int64(now + 3600));
    json_object_object_add(payload, "iat", json_object_new_int64(now));

    const char *header_str = json_object_to_json_string(header);
    const char *payload_str = json_object_to_json_string(payload);

    /* For now, return a placeholder token */
    /* Real implementation would base64url encode and sign with RSA */
    char *token = malloc(1024);
    snprintf(token, 1024, "placeholder_jwt_token_%ld", now);

    json_object_put(header);
    json_object_put(payload);

    return token;
}

/*
 * Get access token from Google OAuth2
 */
static int google_get_access_token(rlm_google_sso_t *inst)
{
    pthread_mutex_lock(&inst->token_mutex);

    /* Check if current token is still valid */
    time_t now = time(NULL);
    if (inst->access_token && inst->token_expires_at > now + 300) {
        pthread_mutex_unlock(&inst->token_mutex);
        return 0; /* Token still valid */
    }

    /* Create JWT assertion */
    char *jwt_token = create_jwt_token(inst, "https://www.googleapis.com/auth/admin.directory.user.readonly");
    if (!jwt_token) {
        pthread_mutex_unlock(&inst->token_mutex);
        return -1;
    }

    /* Prepare HTTP request */
    http_response_t response = {0};
    curl_easy_setopt(inst->curl_handle, CURLOPT_URL, "https://oauth2.googleapis.com/token");
    curl_easy_setopt(inst->curl_handle, CURLOPT_POSTFIELDS,
                     "grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=");
    curl_easy_setopt(inst->curl_handle, CURLOPT_WRITEFUNCTION, http_write_callback);
    curl_easy_setopt(inst->curl_handle, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(inst->curl_handle, CURLOPT_TIMEOUT, inst->api_timeout);

    CURLcode res = curl_easy_perform(inst->curl_handle);
    curl_easy_getinfo(inst->curl_handle, CURLINFO_RESPONSE_CODE, &response.response_code);

    free(jwt_token);

    if (res != CURLE_OK || response.response_code != 200) {
        if (response.data) free(response.data);
        pthread_mutex_unlock(&inst->token_mutex);
        return -1;
    }

    /* Parse JSON response */
    json_object *json_response = json_tokener_parse(response.data);
    if (!json_response) {
        free(response.data);
        pthread_mutex_unlock(&inst->token_mutex);
        return -1;
    }

    json_object *access_token_obj;
    if (json_object_object_get_ex(json_response, "access_token", &access_token_obj)) {
        if (inst->access_token) free(inst->access_token);
        inst->access_token = strdup(json_object_get_string(access_token_obj));
        inst->token_expires_at = now + inst->token_cache_ttl;
    }

    json_object_put(json_response);
    free(response.data);
    pthread_mutex_unlock(&inst->token_mutex);

    return inst->access_token ? 0 : -1;
}

/*
 * Get user information from Google Directory API
 */
static google_user_t *google_get_user_info(rlm_google_sso_t *inst, const char *username)
{
    if (google_get_access_token(inst) < 0) {
        return NULL;
    }

    /* Prepare API request URL */
    char url[512];
    snprintf(url, sizeof(url), "%s/admin/directory/v1/users/%s", inst->api_base_url, username);

    /* Prepare authorization header */
    char auth_header[1024];
    snprintf(auth_header, sizeof(auth_header), "Authorization: Bearer %s", inst->access_token);

    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, auth_header);
    headers = curl_slist_append(headers, "Content-Type: application/json");

    http_response_t response = {0};
    curl_easy_setopt(inst->curl_handle, CURLOPT_URL, url);
    curl_easy_setopt(inst->curl_handle, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(inst->curl_handle, CURLOPT_WRITEFUNCTION, http_write_callback);
    curl_easy_setopt(inst->curl_handle, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(inst->curl_handle, CURLOPT_TIMEOUT, inst->api_timeout);
    curl_easy_setopt(inst->curl_handle, CURLOPT_HTTPGET, 1L);

    CURLcode res = curl_easy_perform(inst->curl_handle);
    curl_easy_getinfo(inst->curl_handle, CURLINFO_RESPONSE_CODE, &response.response_code);

    curl_slist_free_all(headers);

    if (res != CURLE_OK || response.response_code != 200) {
        if (response.data) free(response.data);
        return NULL;
    }

    /* Parse JSON response */
    json_object *json_response = json_tokener_parse(response.data);
    if (!json_response) {
        free(response.data);
        return NULL;
    }

    google_user_t *user = calloc(1, sizeof(google_user_t));
    if (!user) {
        json_object_put(json_response);
        free(response.data);
        return NULL;
    }

    /* Extract user information */
    json_object *email_obj, *name_obj, *id_obj, *suspended_obj;

    if (json_object_object_get_ex(json_response, "primaryEmail", &email_obj)) {
        user->email = strdup(json_object_get_string(email_obj));
    }

    if (json_object_object_get_ex(json_response, "name", &name_obj)) {
        json_object *full_name_obj;
        if (json_object_object_get_ex(name_obj, "fullName", &full_name_obj)) {
            user->name = strdup(json_object_get_string(full_name_obj));
        }
    }

    if (json_object_object_get_ex(json_response, "id", &id_obj)) {
        user->id = strdup(json_object_get_string(id_obj));
    }

    if (json_object_object_get_ex(json_response, "suspended", &suspended_obj)) {
        user->is_active = !json_object_get_boolean(suspended_obj);
    } else {
        user->is_active = true;
    }

    user->cached_at = time(NULL);

    json_object_put(json_response);
    free(response.data);

    return user;
}

/*
 * Check if user has required group membership
 */
static bool check_group_membership(rlm_google_sso_t *inst, google_user_t *user)
{
    if (!inst->required_groups || !user->groups) {
        return true; /* No requirements or no groups to check */
    }

    /* Simple implementation - check if user is in any required group */
    /* Real implementation would parse comma-separated list and check each */
    for (size_t i = 0; i < user->group_count; i++) {
        if (strstr(inst->required_groups, user->groups[i])) {
            return true;
        }
    }

    return false;
}

/*
 * Free google_user_t structure
 */
static void google_user_free(google_user_t *user)
{
    if (!user) return;

    free(user->email);
    free(user->name);
    free(user->id);

    if (user->groups) {
        for (size_t i = 0; i < user->group_count; i++) {
            free(user->groups[i]);
        }
        free(user->groups);
    }

    free(user);
}

/*
 * The module name should be the only globally exported symbol.
 * That is, everything else should be 'static'.
 */
extern module_t rlm_google_sso;
module_t rlm_google_sso = {
    .magic      = RLM_MODULE_INIT,
    .name       = "google_sso",
    .type       = RLM_TYPE_THREAD_SAFE,
    .inst_size  = sizeof(rlm_google_sso_t),
    .config     = module_config,
    .bootstrap  = mod_bootstrap,
    .instantiate = mod_instantiate,
    .detach     = mod_detach,
    .methods = {
        [MOD_AUTHORIZE]     = mod_authorize,
        [MOD_AUTHENTICATE]  = mod_authenticate,
        [MOD_POST_AUTH]     = mod_post_auth,
    },
};
