# FreeRADIUS Google SSO Module - Deployment Guide

This guide will walk you through deploying the FreeRADIUS Google SSO module from development (macOS) to production (Linux server).

## Overview

Since you're developing on macOS, we'll:
1. Prepare the deployment package
2. Set up a Linux server (Ubuntu/Debian recommended)
3. Deploy and configure the module
4. Set up Google Workspace integration
5. Test the deployment

## Step 1: Prepare Deployment Package

### Create Deployment Archive

```bash
# From your project directory
tar -czf freeradius-google-sso-deployment.tar.gz \
    --exclude='cmake-build-debug' \
    --exclude='.git' \
    --exclude='*.o' \
    --exclude='*.so' \
    .

echo "Deployment package created: freeradius-google-sso-deployment.tar.gz"
```

### Verify Package Contents

```bash
tar -tzf freeradius-google-sso-deployment.tar.gz | head -20
```

## Step 2: Set Up Target Server

### Server Requirements

- **OS**: Ubuntu 20.04+ or Debian 11+ (recommended)
- **RAM**: Minimum 2GB, 4GB+ recommended
- **CPU**: 2+ cores recommended
- **Storage**: 20GB+ available space
- **Network**: Internet access for Google API calls

### Access Your Server

```bash
# SSH to your server
ssh user@your-server-ip

# Or if using a cloud provider, use their console
```

## Step 3: Transfer and Extract Files

### Transfer the Package

```bash
# From your macOS machine
scp freeradius-google-sso-deployment.tar.gz user@your-server-ip:/tmp/

# SSH to server and extract
ssh user@your-server-ip
cd /tmp
tar -xzf freeradius-google-sso-deployment.tar.gz
cd freeradius-google-sso
```

## Step 4: Install Dependencies and Build

### Run the Setup Script

```bash
# Make setup script executable
chmod +x setup.sh

# Install dependencies (requires sudo)
sudo ./setup.sh dependencies

# Build the module
./setup.sh build

# Install the module
sudo ./setup.sh install
```

### Verify Installation

```bash
# Test the installation
./test_module.sh dependencies
./test_module.sh loading
./test_module.sh config
```

## Step 5: Configure Google Workspace

### Create Service Account

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create/Select Project**: Create a new project or select existing
3. **Enable APIs**:
   - Admin SDK API
   - Google Workspace Admin SDK
4. **Create Service Account**:
   - Name: `freeradius-auth`
   - Download JSON key file

### Configure Domain-Wide Delegation

1. **Google Admin Console**: https://admin.google.com/
2. **Security > API Controls > Domain-wide Delegation**
3. **Add Client ID** with scopes:
   ```
   https://www.googleapis.com/auth/admin.directory.user.readonly,
   https://www.googleapis.com/auth/admin.directory.group.readonly
   ```

### Transfer Service Account Key

```bash
# From your macOS machine (after downloading from Google)
scp /path/to/service-account-key.json user@your-server-ip:/tmp/google-service-account.json

# On server, move to secure location
sudo mkdir -p /etc/freeradius/certs
sudo mv /tmp/google-service-account.json /etc/freeradius/certs/
sudo chown freerad:freerad /etc/freeradius/certs/google-service-account.json
sudo chmod 600 /etc/freeradius/certs/google-service-account.json
```

## Step 6: Configure FreeRADIUS Module

### Edit Module Configuration

```bash
sudo nano /etc/freeradius/mods-enabled/google_sso
```

Update with your settings:
```
google_sso {
    service_account_email = "<EMAIL>"
    service_account_key = "/etc/freeradius/certs/google-service-account.json"
    domain = "yourdomain.com"
    admin_email = "<EMAIL>"
    
    # Security settings
    require_group_membership = yes
    required_groups = "wifi-users,employees"
    
    # Enable debug for initial testing
    debug_mode = yes
}
```

### Update Virtual Servers

#### Default Site
```bash
sudo nano /etc/freeradius/sites-enabled/default
```

Add to authorize section:
```
authorize {
    # ... existing modules ...
    google_sso
    # ... rest of modules ...
}
```

Add to authenticate section:
```
authenticate {
    # ... existing auth types ...
    
    Auth-Type google_sso {
        google_sso
    }
}
```

Add to post-auth section:
```
post-auth {
    # ... existing modules ...
    google_sso
    # ... rest of modules ...
}
```

#### Inner Tunnel (for EAP)
```bash
sudo nano /etc/freeradius/sites-enabled/inner-tunnel
```

Add similar sections as above.

## Step 7: Test the Configuration

### Test FreeRADIUS Configuration

```bash
# Test configuration syntax
sudo freeradius -C

# Test module loading
sudo freeradius -X | grep google_sso
```

### Test Google API Connectivity

```bash
# Test basic connectivity
./test_module.sh google-api

# Test full configuration
./test_module.sh all
```

### Test Authentication

```bash
# Test with a real user (replace with actual credentials)
radtest <EMAIL> userpassword localhost 0 testing123
```

## Step 8: Configure WiFi Access Point

### Example Configuration (varies by vendor)

#### Ubiquiti UniFi
1. **Create RADIUS Profile**:
   - Auth Server: your-server-ip
   - Auth Port: 1812
   - Auth Secret: testing123
   - Acct Server: your-server-ip
   - Acct Port: 1813
   - Acct Secret: testing123

2. **Create WiFi Network**:
   - Security: WPA2-Enterprise
   - RADIUS Profile: (select created profile)

#### Cisco/Meraki
```
# Example configuration
dot1x system-auth-control
aaa new-model
aaa authentication dot1x default group radius
radius server google-sso
 address ipv4 your-server-ip auth-port 1812 acct-port 1813
 key testing123
```

## Step 9: Client Configuration

### Windows 10/11
1. **WiFi Settings** > Advanced Options
2. **Security**: WPA2-Enterprise
3. **EAP Type**: TTLS
4. **Authentication**: PAP
5. **Username**: <EMAIL>
6. **Password**: Google Workspace password

### macOS/iOS
1. **WiFi Settings** > Configure
2. **Security**: WPA2 Enterprise
3. **Authentication**: TTLS
4. **Inner Authentication**: PAP
5. **Username**: <EMAIL>
6. **Password**: Google Workspace password

### Android
1. **WiFi Settings** > Advanced
2. **EAP Method**: TTLS
3. **Phase 2 Authentication**: PAP
4. **Identity**: <EMAIL>
5. **Password**: Google Workspace password

## Step 10: Production Hardening

### Security Configuration

```bash
# Disable debug mode
sudo nano /etc/freeradius/mods-enabled/google_sso
# Set: debug_mode = no

# Secure file permissions
sudo chmod 600 /etc/freeradius/mods-enabled/google_sso
sudo chown freerad:freerad /etc/freeradius/mods-enabled/google_sso

# Configure firewall
sudo ufw allow 1812/udp  # RADIUS auth
sudo ufw allow 1813/udp  # RADIUS accounting
sudo ufw enable
```

### Monitoring Setup

```bash
# Enable detailed logging
sudo nano /etc/freeradius/radiusd.conf
# Uncomment: log_file = ${logdir}/radius.log

# Set up log rotation
sudo nano /etc/logrotate.d/freeradius
```

Add:
```
/var/log/freeradius/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 640 freerad freerad
    postrotate
        systemctl reload freeradius
    endscript
}
```

### Service Management

```bash
# Enable FreeRADIUS service
sudo systemctl enable freeradius
sudo systemctl start freeradius

# Check status
sudo systemctl status freeradius

# View logs
sudo journalctl -u freeradius -f
```

## Troubleshooting

### Common Issues

1. **Module fails to load**:
   ```bash
   # Check dependencies
   ldd /usr/lib/freeradius/rlm_google_sso.so
   
   # Check FreeRADIUS logs
   sudo freeradius -X
   ```

2. **Google API authentication fails**:
   ```bash
   # Verify service account key
   sudo -u freerad cat /etc/freeradius/certs/google-service-account.json
   
   # Test API connectivity
   curl -s https://www.googleapis.com/admin/directory/v1/users
   ```

3. **User authentication fails**:
   ```bash
   # Check user exists in Google Workspace
   # Verify group membership
   # Check FreeRADIUS debug output
   sudo freeradius -X
   ```

### Debug Commands

```bash
# Full debug mode
sudo freeradius -X

# Test specific user
radtest <EMAIL> password localhost 0 testing123

# Check module status
./test_module.sh all

# Monitor real-time logs
sudo tail -f /var/log/freeradius/radius.log
```

## Next Steps

1. **Test with real users** from your Google Workspace
2. **Configure VLAN assignment** based on groups
3. **Set up monitoring** and alerting
4. **Document your configuration** for team members
5. **Plan backup and recovery** procedures

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Google Workspace setup in `docs/google_workspace_setup.md`
3. Run the test suite: `./test_module.sh all`
4. Check FreeRADIUS logs with debug enabled
