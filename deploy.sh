#!/bin/bash

#
# deploy.sh - Quick deployment script for FreeRADIUS Google SSO Module
#
# This script automates the deployment process on a target Linux server
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_PACKAGE="freeradius-google-sso-deployment.tar.gz"
WORK_DIR="/tmp/freeradius-google-sso-deploy"
SERVICE_ACCOUNT_KEY=""
GOOGLE_DOMAIN=""
ADMIN_EMAIL=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root for certain operations
check_sudo() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This is okay for deployment."
    elif ! sudo -n true 2>/dev/null; then
        print_error "This script requires sudo access. Please ensure you can run sudo commands."
        exit 1
    fi
}

# Function to collect configuration information
collect_config() {
    print_status "Collecting configuration information..."
    
    echo
    echo "Please provide the following information for your deployment:"
    echo
    
    # Google Workspace domain
    while [[ -z "$GOOGLE_DOMAIN" ]]; do
        read -p "Google Workspace domain (e.g., company.com): " GOOGLE_DOMAIN
        if [[ ! "$GOOGLE_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$ ]]; then
            print_error "Invalid domain format. Please enter a valid domain."
            GOOGLE_DOMAIN=""
        fi
    done
    
    # Admin email
    while [[ -z "$ADMIN_EMAIL" ]]; do
        read -p "Google Workspace admin email: " ADMIN_EMAIL
        if [[ ! "$ADMIN_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            print_error "Invalid email format. Please enter a valid email."
            ADMIN_EMAIL=""
        fi
    done
    
    # Service account key file
    while [[ -z "$SERVICE_ACCOUNT_KEY" || ! -f "$SERVICE_ACCOUNT_KEY" ]]; do
        read -p "Path to Google service account JSON key file: " SERVICE_ACCOUNT_KEY
        if [[ ! -f "$SERVICE_ACCOUNT_KEY" ]]; then
            print_error "File not found. Please provide the correct path."
            SERVICE_ACCOUNT_KEY=""
        fi
    done
    
    print_success "Configuration collected successfully"
}

# Function to extract deployment package
extract_package() {
    print_status "Extracting deployment package..."
    
    if [[ ! -f "$DEPLOYMENT_PACKAGE" ]]; then
        print_error "Deployment package not found: $DEPLOYMENT_PACKAGE"
        print_warning "Please ensure the package is in the current directory"
        exit 1
    fi
    
    # Create work directory
    rm -rf "$WORK_DIR"
    mkdir -p "$WORK_DIR"
    
    # Extract package
    tar -xzf "$DEPLOYMENT_PACKAGE" -C "$WORK_DIR"
    
    print_success "Package extracted to $WORK_DIR"
}

# Function to run the installation
run_installation() {
    print_status "Running installation..."
    
    cd "$WORK_DIR"
    
    # Make scripts executable
    chmod +x setup.sh test_module.sh
    
    # Install dependencies
    print_status "Installing dependencies..."
    sudo ./setup.sh dependencies
    
    # Build module
    print_status "Building module..."
    ./setup.sh build
    
    # Install module
    print_status "Installing module..."
    sudo ./setup.sh install
    
    print_success "Installation completed"
}

# Function to configure the module
configure_module() {
    print_status "Configuring module..."
    
    # Install service account key
    sudo mkdir -p /etc/freeradius/certs
    sudo cp "$SERVICE_ACCOUNT_KEY" /etc/freeradius/certs/google-service-account.json
    sudo chown freerad:freerad /etc/freeradius/certs/google-service-account.json
    sudo chmod 600 /etc/freeradius/certs/google-service-account.json
    
    # Extract service account email from JSON
    SERVICE_ACCOUNT_EMAIL=$(python3 -c "
import json
with open('$SERVICE_ACCOUNT_KEY', 'r') as f:
    data = json.load(f)
    print(data['client_email'])
" 2>/dev/null || echo "")
    
    if [[ -z "$SERVICE_ACCOUNT_EMAIL" ]]; then
        print_warning "Could not extract service account email from JSON file"
        read -p "Please enter the service account email: " SERVICE_ACCOUNT_EMAIL
    fi
    
    # Update module configuration
    sudo tee /etc/freeradius/mods-enabled/google_sso > /dev/null << EOF
google_sso {
    service_account_email = "$SERVICE_ACCOUNT_EMAIL"
    service_account_key = "/etc/freeradius/certs/google-service-account.json"
    domain = "$GOOGLE_DOMAIN"
    admin_email = "$ADMIN_EMAIL"
    
    # API Configuration
    api_timeout = 30
    token_cache_ttl = 3600
    
    # Authentication Configuration
    enable_certificate_auth = no
    enable_password_fallback = yes
    
    # Security Configuration
    max_auth_attempts = 5
    lockout_duration = 900
    require_group_membership = no
    
    # Caching Configuration
    user_cache_ttl = 300
    group_cache_ttl = 600
    enable_offline_auth = yes
    
    # Logging Configuration
    debug_mode = yes
}
EOF
    
    print_success "Module configuration updated"
}

# Function to update FreeRADIUS sites
update_sites() {
    print_status "Updating FreeRADIUS virtual servers..."
    
    # Backup original files
    sudo cp /etc/freeradius/sites-available/default /etc/freeradius/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)
    sudo cp /etc/freeradius/sites-available/inner-tunnel /etc/freeradius/sites-available/inner-tunnel.backup.$(date +%Y%m%d_%H%M%S)
    
    print_warning "Virtual server configuration requires manual editing."
    print_warning "Please add 'google_sso' to the authorize, authenticate, and post-auth sections."
    print_warning "See DEPLOYMENT_GUIDE.md for detailed instructions."
    
    print_success "Backup files created"
}

# Function to test the deployment
test_deployment() {
    print_status "Testing deployment..."
    
    cd "$WORK_DIR"
    
    # Run tests
    ./test_module.sh dependencies
    ./test_module.sh loading
    ./test_module.sh config
    
    # Test FreeRADIUS configuration
    if sudo freeradius -C; then
        print_success "FreeRADIUS configuration test passed"
    else
        print_error "FreeRADIUS configuration test failed"
        return 1
    fi
    
    print_success "Basic tests completed"
}

# Function to show next steps
show_next_steps() {
    print_success "Deployment completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Complete FreeRADIUS virtual server configuration:"
    echo "   - Edit /etc/freeradius/sites-enabled/default"
    echo "   - Edit /etc/freeradius/sites-enabled/inner-tunnel"
    echo "   - Add 'google_sso' to authorize, authenticate, and post-auth sections"
    echo
    echo "2. Test authentication:"
    echo "   sudo freeradius -X"
    echo "   radtest user@$GOOGLE_DOMAIN password localhost 0 testing123"
    echo
    echo "3. Configure your WiFi access points with:"
    echo "   - RADIUS Server: $(hostname -I | awk '{print $1}')"
    echo "   - Auth Port: 1812"
    echo "   - Acct Port: 1813"
    echo "   - Shared Secret: testing123 (change this!)"
    echo
    echo "4. Configure client devices for:"
    echo "   - Security: WPA2-Enterprise"
    echo "   - EAP Method: TTLS"
    echo "   - Phase 2 Auth: PAP"
    echo "   - Username: user@$GOOGLE_DOMAIN"
    echo
    echo "For detailed instructions, see:"
    echo "- DEPLOYMENT_GUIDE.md"
    echo "- docs/google_workspace_setup.md"
    echo "- examples/client_configs.md"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --package FILE    Specify deployment package file"
    echo "  --key FILE        Specify service account key file"
    echo "  --domain DOMAIN   Specify Google Workspace domain"
    echo "  --admin EMAIL     Specify admin email"
    echo "  --help           Show this help message"
    echo
    echo "Interactive mode (recommended):"
    echo "  $0"
    echo
    echo "Non-interactive mode:"
    echo "  $0 --package freeradius-google-sso-deployment.tar.gz \\"
    echo "     --key /path/to/service-account.json \\"
    echo "     --domain company.com \\"
    echo "     --admin <EMAIL>"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --package)
            DEPLOYMENT_PACKAGE="$2"
            shift 2
            ;;
        --key)
            SERVICE_ACCOUNT_KEY="$2"
            shift 2
            ;;
        --domain)
            GOOGLE_DOMAIN="$2"
            shift 2
            ;;
        --admin)
            ADMIN_EMAIL="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main deployment function
main() {
    print_status "Starting FreeRADIUS Google SSO deployment..."
    
    check_sudo
    
    # Collect configuration if not provided via command line
    if [[ -z "$GOOGLE_DOMAIN" || -z "$ADMIN_EMAIL" || -z "$SERVICE_ACCOUNT_KEY" ]]; then
        collect_config
    fi
    
    extract_package
    run_installation
    configure_module
    update_sites
    
    if test_deployment; then
        show_next_steps
    else
        print_error "Deployment completed with errors. Please check the output above."
        exit 1
    fi
}

# Run main function
main "$@"
