#!/bin/bash

#
# setup.sh - Installation script for FreeRADIUS Google SSO Module
#
# This script automates the installation and configuration of the
# rlm_google_sso module for FreeRADIUS
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
FREERADIUS_CONFIG_DIR="/etc/freeradius"
FREERADIUS_LIB_DIR="/usr/lib/freeradius"
MODULE_NAME="rlm_google_sso"
BUILD_DIR="build"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Function to detect OS and install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y build-essential cmake pkg-config \
            libcurl4-openssl-dev libjson-c-dev libssl-dev \
            freeradius freeradius-utils
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        yum install -y gcc make cmake pkgconfig \
            libcurl-devel json-c-devel openssl-devel \
            freeradius freeradius-utils
    elif command -v dnf &> /dev/null; then
        # Fedora
        dnf install -y gcc make cmake pkgconfig \
            libcurl-devel json-c-devel openssl-devel \
            freeradius freeradius-utils
    else
        print_error "Unsupported operating system"
        exit 1
    fi
    
    print_success "Dependencies installed"
}

# Function to build the module
build_module() {
    print_status "Building the module..."
    
    if [[ ! -d "$BUILD_DIR" ]]; then
        mkdir "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR"
    cmake ..
    make
    cd ..
    
    print_success "Module built successfully"
}

# Function to install the module
install_module() {
    print_status "Installing the module..."
    
    # Install the shared library
    if [[ -f "$BUILD_DIR/librlm_google_sso.so" ]]; then
        cp "$BUILD_DIR/librlm_google_sso.so" "$FREERADIUS_LIB_DIR/"
        chmod 755 "$FREERADIUS_LIB_DIR/librlm_google_sso.so"
    else
        print_error "Module library not found in build directory"
        exit 1
    fi
    
    # Install dictionary
    cp dictionary.google_sso "$FREERADIUS_CONFIG_DIR/"
    
    # Install configuration template
    cp config/google_sso.conf "$FREERADIUS_CONFIG_DIR/mods-available/google_sso"
    
    # Update main dictionary
    if ! grep -q "dictionary.google_sso" "$FREERADIUS_CONFIG_DIR/dictionary"; then
        echo '$INCLUDE dictionary.google_sso' >> "$FREERADIUS_CONFIG_DIR/dictionary"
    fi
    
    print_success "Module installed"
}

# Function to configure FreeRADIUS
configure_freeradius() {
    print_status "Configuring FreeRADIUS..."
    
    # Enable the module
    if [[ ! -L "$FREERADIUS_CONFIG_DIR/mods-enabled/google_sso" ]]; then
        ln -s "$FREERADIUS_CONFIG_DIR/mods-available/google_sso" \
              "$FREERADIUS_CONFIG_DIR/mods-enabled/google_sso"
    fi
    
    # Backup original configuration files
    if [[ ! -f "$FREERADIUS_CONFIG_DIR/sites-available/default.backup" ]]; then
        cp "$FREERADIUS_CONFIG_DIR/sites-available/default" \
           "$FREERADIUS_CONFIG_DIR/sites-available/default.backup"
    fi
    
    if [[ ! -f "$FREERADIUS_CONFIG_DIR/sites-available/inner-tunnel.backup" ]]; then
        cp "$FREERADIUS_CONFIG_DIR/sites-available/inner-tunnel" \
           "$FREERADIUS_CONFIG_DIR/sites-available/inner-tunnel.backup"
    fi
    
    print_warning "Manual configuration required:"
    print_warning "1. Edit $FREERADIUS_CONFIG_DIR/mods-enabled/google_sso"
    print_warning "2. Add 'google_sso' to authorize, authenticate, and post-auth sections"
    print_warning "3. Configure your Google Workspace service account"
    
    print_success "Basic configuration completed"
}

# Function to create certificates directory
setup_certificates() {
    print_status "Setting up certificates directory..."
    
    CERT_DIR="$FREERADIUS_CONFIG_DIR/certs"
    
    if [[ ! -d "$CERT_DIR" ]]; then
        mkdir -p "$CERT_DIR"
    fi
    
    chown freerad:freerad "$CERT_DIR"
    chmod 750 "$CERT_DIR"
    
    print_success "Certificates directory created"
    print_warning "Place your Google service account key in $CERT_DIR/"
}

# Function to test the installation
test_installation() {
    print_status "Testing the installation..."
    
    # Check if FreeRADIUS can load the module
    if freeradius -C &> /dev/null; then
        print_success "FreeRADIUS configuration test passed"
    else
        print_error "FreeRADIUS configuration test failed"
        print_warning "Run 'freeradius -X' to see detailed error messages"
        return 1
    fi
    
    # Check if module is loaded
    if freeradius -X 2>&1 | grep -q "rlm_google_sso"; then
        print_success "Module loaded successfully"
    else
        print_warning "Module may not be loaded - check configuration"
    fi
}

# Function to display post-installation instructions
show_instructions() {
    print_success "Installation completed!"
    echo
    echo "Next steps:"
    echo "1. Configure Google Workspace:"
    echo "   - Create a service account with domain-wide delegation"
    echo "   - Download the service account key (JSON format)"
    echo "   - Place the key in $FREERADIUS_CONFIG_DIR/certs/"
    echo
    echo "2. Edit the module configuration:"
    echo "   - Edit $FREERADIUS_CONFIG_DIR/mods-enabled/google_sso"
    echo "   - Update service_account_email, domain, and admin_email"
    echo "   - Set the correct path to your service account key"
    echo
    echo "3. Update FreeRADIUS virtual servers:"
    echo "   - Add 'google_sso' to the authorize section"
    echo "   - Add 'Auth-Type google_sso { google_sso }' to authenticate section"
    echo "   - Add 'google_sso' to the post-auth section"
    echo
    echo "4. Test the configuration:"
    echo "   - Run 'freeradius -X' to test in debug mode"
    echo "   - Use 'radtest <EMAIL> password localhost 0 testing123'"
    echo
    echo "For detailed configuration instructions, see README.md"
}

# Main installation function
main() {
    print_status "Starting FreeRADIUS Google SSO Module installation..."
    
    check_root
    install_dependencies
    build_module
    install_module
    configure_freeradius
    setup_certificates
    
    if test_installation; then
        show_instructions
    else
        print_error "Installation completed with warnings"
        print_warning "Please check the configuration and run tests manually"
    fi
}

# Parse command line arguments
case "${1:-install}" in
    install)
        main
        ;;
    dependencies)
        check_root
        install_dependencies
        ;;
    build)
        build_module
        ;;
    test)
        test_installation
        ;;
    clean)
        print_status "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
        print_success "Build directory cleaned"
        ;;
    uninstall)
        check_root
        print_status "Uninstalling module..."
        rm -f "$FREERADIUS_LIB_DIR/librlm_google_sso.so"
        rm -f "$FREERADIUS_CONFIG_DIR/dictionary.google_sso"
        rm -f "$FREERADIUS_CONFIG_DIR/mods-enabled/google_sso"
        rm -f "$FREERADIUS_CONFIG_DIR/mods-available/google_sso"
        print_success "Module uninstalled"
        ;;
    *)
        echo "Usage: $0 {install|dependencies|build|test|clean|uninstall}"
        echo
        echo "Commands:"
        echo "  install      - Full installation (default)"
        echo "  dependencies - Install only dependencies"
        echo "  build        - Build the module only"
        echo "  test         - Test the installation"
        echo "  clean        - Clean build directory"
        echo "  uninstall    - Remove the module"
        exit 1
        ;;
esac
