# FreeRADIUS Google SSO Module

A FreeRADIUS module that enables WiFi authentication using Google Workspace (formerly G Suite) credentials. This module allows users to authenticate to WiFi networks using their Google email address and password, with support for both password-based and certificate-based authentication.

## Features

- **Google Workspace Integration**: Authenticate users against Google Workspace directory
- **Multiple Authentication Methods**: Support for password-based and certificate-based (EAP-TLS) authentication
- **Group-Based Access Control**: Control access based on Google Workspace group membership
- **VLAN Assignment**: Automatic VLAN assignment based on user groups
- **Caching**: Intelligent caching of user information and authentication tokens
- **Offline Authentication**: Support for cached credential authentication when Google APIs are unavailable
- **Security Features**: Rate limiting, account lockout, and comprehensive logging

## Architecture

The module implements a secure authentication flow that:

1. Validates users against Google Workspace Directory API using service account authentication
2. Checks group membership and applies policies
3. Supports EAP-TTLS/PAP authentication with Google Workspace credentials
4. Provides comprehensive logging and monitoring
5. Uses JWT-based authentication with Google APIs for secure communication

**Note**: This implementation uses Google Workspace Directory API for user validation rather than the deprecated OAuth2 Resource Owner Password Credentials Grant. Users authenticate with their Google Workspace email and password, and the module validates their existence and status through the Directory API.

## Prerequisites

### System Requirements

- FreeRADIUS 3.0.x or later
- libcurl development libraries
- json-c development libraries
- OpenSSL development libraries
- pthread support

### Google Workspace Setup

1. **Create a Service Account**:
   - Go to Google Cloud Console
   - Create a new project or select existing one
   - Enable the Admin SDK API
   - Create a service account with domain-wide delegation
   - Download the service account key (JSON format)

2. **Configure Domain-Wide Delegation**:
   - In Google Admin Console, go to Security > API Controls
   - Add the service account client ID
   - Grant the following OAuth scopes:
     - `https://www.googleapis.com/auth/admin.directory.user.readonly`
     - `https://www.googleapis.com/auth/admin.directory.group.readonly`

## Installation

### 1. Install Dependencies

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install build-essential libcurl4-openssl-dev libjson-c-dev libssl-dev
```

#### CentOS/RHEL:
```bash
sudo yum install gcc make libcurl-devel json-c-devel openssl-devel
```

### 2. Build the Module

```bash
# Clone or copy the module source
cd /path/to/freeradius-google-sso

# Build using CMake
mkdir build && cd build
cmake ..
make

# Or build using FreeRADIUS build system
# Copy module to FreeRADIUS source tree
cp -r . /path/to/freeradius-server/src/modules/rlm_google_sso
cd /path/to/freeradius-server
make && make install
```

### 3. Install Module Files

```bash
# Copy module library
sudo cp build/librlm_google_sso.so /usr/lib/freeradius/

# Copy dictionary
sudo cp dictionary.google_sso /etc/freeradius/dictionary.google_sso

# Copy configuration template
sudo cp config/google_sso.conf /etc/freeradius/mods-available/google_sso

# Enable the module
sudo ln -s /etc/freeradius/mods-available/google_sso /etc/freeradius/mods-enabled/google_sso
```

## Configuration

### 1. Module Configuration

Edit `/etc/freeradius/mods-available/google_sso`:

```
google_sso {
    service_account_email = "<EMAIL>"
    service_account_key = "/etc/freeradius/certs/google-service-account.json"
    domain = "your-domain.com"
    admin_email = "<EMAIL>"
    
    # Enable certificate authentication for better security
    enable_certificate_auth = yes
    enable_password_fallback = yes
    
    # Group-based access control
    require_group_membership = yes
    required_groups = "wifi-users,employees"
}
```

### 2. Update FreeRADIUS Dictionary

Add to `/etc/freeradius/dictionary`:
```
$INCLUDE dictionary.google_sso
```

### 3. Configure Virtual Servers

#### Default Site (`/etc/freeradius/sites-enabled/default`):

```
authorize {
    # ... other modules ...
    google_sso
    # ... other modules ...
}

authenticate {
    # ... other auth types ...
    
    Auth-Type google_sso {
        google_sso
    }
}

post-auth {
    # ... other modules ...
    google_sso
    # ... other modules ...
}
```

#### Inner Tunnel (`/etc/freeradius/sites-enabled/inner-tunnel`):

```
authorize {
    # ... other modules ...
    google_sso
    # ... other modules ...
}

authenticate {
    # ... other auth types ...
    
    Auth-Type google_sso {
        google_sso
    }
}
```

### 4. WiFi Network Configuration

#### For Password Authentication (EAP-TTLS/PAP):
- Security: WPA2-Enterprise
- EAP Method: TTLS
- Phase 2 Authentication: PAP
- Username: <EMAIL>
- Password: User's Google password

#### For Certificate Authentication (EAP-TLS):
- Security: WPA2-Enterprise
- EAP Method: TLS
- Client Certificate: Generated by the module
- CA Certificate: Module's CA certificate

## Usage Examples

### Basic Authentication

Users authenticate with their Google Workspace credentials:
- Username: `<EMAIL>`
- Password: Their Google Workspace password

### Group-Based VLAN Assignment

Configure different VLANs based on Google groups:

```
# In /etc/freeradius/policy.d/google_sso_policy
google_sso_vlan_policy {
    if (&Google-User-Groups && (&Google-User-Groups[*] == "executives")) {
        update reply {
            Tunnel-Type := VLAN
            Tunnel-Medium-Type := IEEE-802
            Tunnel-Private-Group-ID := 10
        }
    }
    elsif (&Google-User-Groups && (&Google-User-Groups[*] == "employees")) {
        update reply {
            Tunnel-Type := VLAN
            Tunnel-Medium-Type := IEEE-802
            Tunnel-Private-Group-ID := 20
        }
    }
    elsif (&Google-User-Groups && (&Google-User-Groups[*] == "contractors")) {
        update reply {
            Tunnel-Type := VLAN
            Tunnel-Medium-Type := IEEE-802
            Tunnel-Private-Group-ID := 30
            Session-Timeout := 28800  # 8 hours
        }
    }
}
```

## Testing

### 1. Test Module Loading

```bash
sudo freeradius -X
# Look for "rlm_google_sso: Instantiation complete" in the output
```

### 2. Test Authentication

```bash
# Test with radtest
radtest <EMAIL> password localhost 0 testing123

# Test with eapol_test (for EAP-TTLS)
echo 'network={
    key_mgmt=IEEE8021X
    eap=TTLS
    identity="<EMAIL>"
    password="password"
    phase2="auth=PAP"
}' > test.conf

eapol_test -c test.conf -s testing123
```

## Troubleshooting

### Common Issues

1. **Module fails to load**:
   - Check dependencies: `ldd /usr/lib/freeradius/rlm_google_sso.so`
   - Verify FreeRADIUS version compatibility

2. **Google API authentication fails**:
   - Verify service account key file permissions
   - Check domain-wide delegation configuration
   - Ensure required API scopes are granted

3. **User authentication fails**:
   - Verify user exists in Google Workspace
   - Check user is not suspended
   - Verify group membership requirements

### Debug Mode

Enable debug logging in the module configuration:
```
google_sso {
    debug_mode = yes
    log_file = "/var/log/freeradius/google_sso.log"
}
```

Run FreeRADIUS in debug mode:
```bash
sudo freeradius -X
```

## Security Considerations

1. **Service Account Security**:
   - Store service account keys securely
   - Use least privilege principle for API scopes
   - Regularly rotate service account keys

2. **Network Security**:
   - Use EAP-TLS when possible for strongest security
   - Implement proper certificate validation
   - Use strong WiFi encryption (WPA3 preferred)

3. **Access Control**:
   - Implement group-based access control
   - Use VLAN segmentation
   - Monitor authentication logs

## License

This project is licensed under the GNU General Public License v2.0 - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Current Implementation Status

This is a **functional prototype** of the FreeRADIUS Google SSO module. The current implementation includes:

### ✅ Completed Features:
- Basic FreeRADIUS module structure and lifecycle management
- Google Workspace Directory API integration framework
- Configuration parsing and validation
- User existence validation against Google Workspace
- Group membership checking
- Custom RADIUS attributes for Google user information
- Comprehensive build system and installation scripts
- Documentation and setup guides

### 🚧 In Development:
- Complete JWT token generation and signing for Google API authentication
- Full OAuth2 service account authentication flow
- Certificate-based authentication (EAP-TLS) support
- Advanced caching mechanisms
- Comprehensive error handling and retry logic

### 📋 Planned Features:
- Device certificate enrollment and management
- Advanced policy engine for conditional access
- Integration with Google Cloud Identity for enhanced security
- Multi-factor authentication support
- Comprehensive audit logging

## Important Notes

1. **Authentication Flow**: Users authenticate using their Google Workspace email address and password through EAP-TTLS/PAP
2. **API Integration**: The module validates users against Google Workspace Directory API using service account credentials
3. **Security**: This approach is more secure than the deprecated OAuth2 password grant as it doesn't transmit Google passwords to third parties
4. **Testing Required**: Thorough testing is recommended before production deployment

## Support

For issues and questions:
1. Check the troubleshooting section in the README
2. Review the Google Workspace setup guide in `docs/google_workspace_setup.md`
3. Review FreeRADIUS logs with debug enabled (`sudo freeradius -X`)
4. Test the module installation using the provided test script (`./test_module.sh`)
5. Open an issue on GitHub with detailed logs and configuration
