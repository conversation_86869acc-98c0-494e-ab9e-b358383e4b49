# Client Configuration Examples

This document provides configuration examples for various devices and operating systems to connect to WiFi networks using the FreeRADIUS Google SSO module.

## Windows 10/11

### EAP-TTLS/PAP Configuration

1. **Open WiFi Settings**:
   - Go to Settings > Network & Internet > WiFi
   - Click on your enterprise WiFi network

2. **Configure Security**:
   - Security type: WPA2-Enterprise or WPA3-Enterprise
   - EAP type: TTLS
   - Authentication method: PAP

3. **Credentials**:
   - Username: `<EMAIL>`
   - Password: User's Google Workspace password

4. **Advanced Settings**:
   - Validate server certificate: Yes
   - Connect to these servers: `radius.your-domain.com`
   - Trusted Root Certification Authorities: Select your CA

### PowerShell Script for Automated Configuration

```powershell
# WiFi profile for Google SSO authentication
$profileXml = @"
<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>CompanyWiFi-GoogleSSO</name>
    <SSIDConfig>
        <SSID>
            <name>CompanyWiFi</name>
        </SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>auto</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2</authentication>
                <encryption>AES</encryption>
                <useOneX>true</useOneX>
            </authEncryption>
            <OneX xmlns="http://www.microsoft.com/networking/OneX/v1">
                <authMode>user</authMode>
                <EAPConfig>
                    <EapHostConfig xmlns="http://www.microsoft.com/provisioning/EapHostConfig">
                        <EapMethod>
                            <Type xmlns="http://www.microsoft.com/provisioning/EapCommon">21</Type>
                            <VendorId xmlns="http://www.microsoft.com/provisioning/EapCommon">0</VendorId>
                            <VendorType xmlns="http://www.microsoft.com/provisioning/EapCommon">0</VendorType>
                        </EapMethod>
                        <Config xmlns="http://www.microsoft.com/provisioning/EapHostConfig">
                            <EapTtls xmlns="http://www.microsoft.com/provisioning/EapTtlsConnectionPropertiesV1">
                                <ServerValidation>
                                    <ServerNames>radius.your-domain.com</ServerNames>
                                    <TrustedRootCA>your-ca-thumbprint</TrustedRootCA>
                                </ServerValidation>
                                <Phase2Authentication>
                                    <PAPAuthentication/>
                                </Phase2Authentication>
                            </EapTtls>
                        </Config>
                    </EapHostConfig>
                </EAPConfig>
            </OneX>
        </security>
    </MSM>
</WLANProfile>
"@

# Add the profile
netsh wlan add profile filename="wifi-profile.xml"
```

## macOS

### System Preferences Configuration

1. **Open Network Preferences**:
   - System Preferences > Network
   - Select WiFi and click Advanced

2. **Add Network**:
   - Network Name: Your WiFi SSID
   - Security: WPA2/WPA3 Enterprise

3. **802.1X Settings**:
   - User Name: `<EMAIL>`
   - Password: User's Google Workspace password
   - Authentication: TTLS
   - Inner Authentication: PAP

### Configuration Profile (.mobileconfig)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PayloadContent</key>
    <array>
        <dict>
            <key>SSID_STR</key>
            <string>CompanyWiFi</string>
            <key>HIDDEN_NETWORK</key>
            <false/>
            <key>AutoJoin</key>
            <true/>
            <key>EncryptionType</key>
            <string>WPA2</string>
            <key>EAPClientConfiguration</key>
            <dict>
                <key>AcceptEAPTypes</key>
                <array>
                    <integer>21</integer>
                </array>
                <key>EAPFASTUsePAC</key>
                <false/>
                <key>EAPFASTProvisionPAC</key>
                <false/>
                <key>TTLSInnerAuthentication</key>
                <string>PAP</string>
                <key>TLSCertificateIsRequired</key>
                <false/>
                <key>TLSAllowTrustExceptions</key>
                <false/>
                <key>PayloadCertificateAnchorUUID</key>
                <array>
                    <string>CA-CERT-UUID</string>
                </array>
            </dict>
            <key>PayloadDisplayName</key>
            <string>Company WiFi - Google SSO</string>
            <key>PayloadIdentifier</key>
            <string>com.company.wifi.googleSSO</string>
            <key>PayloadType</key>
            <string>com.apple.wifi.managed</string>
            <key>PayloadUUID</key>
            <string>UNIQUE-UUID-HERE</string>
            <key>PayloadVersion</key>
            <integer>1</integer>
        </dict>
    </array>
    <key>PayloadDisplayName</key>
    <string>Company WiFi Configuration</string>
    <key>PayloadIdentifier</key>
    <string>com.company.wifi</string>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string>UNIQUE-UUID-HERE</string>
    <key>PayloadVersion</key>
    <integer>1</integer>
</dict>
</plist>
```

## Linux (NetworkManager)

### Command Line Configuration

```bash
# Create connection
nmcli connection add type wifi con-name "CompanyWiFi-GoogleSSO" \
    wifi.ssid "CompanyWiFi" \
    wifi-sec.key-mgmt wpa-eap \
    802-1x.eap ttls \
    802-1x.phase2-auth pap \
    802-1x.identity "<EMAIL>" \
    802-1x.ca-cert "/path/to/ca-cert.pem"

# Connect (will prompt for password)
nmcli connection up "CompanyWiFi-GoogleSSO"
```

### wpa_supplicant Configuration

```bash
# /etc/wpa_supplicant/wpa_supplicant.conf
network={
    ssid="CompanyWiFi"
    key_mgmt=IEEE8021X
    eap=TTLS
    identity="<EMAIL>"
    password="user-password"
    phase2="auth=PAP"
    ca_cert="/path/to/ca-cert.pem"
    domain_match="radius.your-domain.com"
}
```

## Android

### Manual Configuration

1. **Open WiFi Settings**:
   - Settings > WiFi
   - Tap your enterprise network

2. **Advanced Options**:
   - EAP method: TTLS
   - Phase 2 authentication: PAP
   - CA certificate: Select your CA cert
   - Identity: `<EMAIL>`
   - Password: User's Google password

### Programmatic Configuration (Android Enterprise)

```json
{
  "wifiConfigurations": [
    {
      "ssid": "CompanyWiFi",
      "security": "WPA2_ENTERPRISE",
      "eapMethod": "TTLS",
      "phase2Method": "PAP",
      "identity": "<EMAIL>",
      "caCertificate": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----",
      "domainSuffixMatch": "radius.your-domain.com"
    }
  ]
}
```

## iOS

### Configuration Profile (.mobileconfig)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PayloadContent</key>
    <array>
        <dict>
            <key>SSID_STR</key>
            <string>CompanyWiFi</string>
            <key>HIDDEN_NETWORK</key>
            <false/>
            <key>AutoJoin</key>
            <true/>
            <key>EncryptionType</key>
            <string>WPA2</string>
            <key>EAPClientConfiguration</key>
            <dict>
                <key>AcceptEAPTypes</key>
                <array>
                    <integer>21</integer>
                </array>
                <key>TTLSInnerAuthentication</key>
                <string>PAP</string>
                <key>TLSCertificateIsRequired</key>
                <false/>
                <key>PayloadCertificateAnchorUUID</key>
                <array>
                    <string>CA-CERT-UUID</string>
                </array>
            </dict>
            <key>PayloadDisplayName</key>
            <string>Company WiFi</string>
            <key>PayloadIdentifier</key>
            <string>com.company.wifi</string>
            <key>PayloadType</key>
            <string>com.apple.wifi.managed</string>
            <key>PayloadUUID</key>
            <string>UNIQUE-UUID-HERE</string>
            <key>PayloadVersion</key>
            <integer>1</integer>
        </dict>
    </array>
    <key>PayloadDisplayName</key>
    <string>Company WiFi Configuration</string>
    <key>PayloadIdentifier</key>
    <string>com.company.wifi</string>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string>UNIQUE-UUID-HERE</string>
    <key>PayloadVersion</key>
    <integer>1</integer>
</dict>
</plist>
```

## ChromeOS

### Enterprise Policy Configuration

```json
{
  "NetworkConfigurations": [
    {
      "GUID": "company-wifi-google-sso",
      "Name": "CompanyWiFi",
      "Type": "WiFi",
      "WiFi": {
        "SSID": "CompanyWiFi",
        "Security": "WPA2-EAP",
        "EAP": {
          "Outer": "TTLS",
          "Inner": "PAP",
          "Identity": "${LOGIN_EMAIL}",
          "UseSystemCAs": false,
          "ServerCAPEMs": ["-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"]
        }
      }
    }
  ]
}
```

## Testing Configuration

### Using eapol_test (Linux)

```bash
# Create test configuration
cat > test-config.conf << EOF
network={
    key_mgmt=IEEE8021X
    eap=TTLS
    identity="<EMAIL>"
    password="test-password"
    phase2="auth=PAP"
    ca_cert="/path/to/ca-cert.pem"
    domain_match="radius.your-domain.com"
}
EOF

# Test authentication
eapol_test -c test-config.conf -s testing123 -a 192.168.1.10
```

### Using radtest

```bash
# Simple test (not EAP)
radtest <EMAIL> test-password 192.168.1.10 0 testing123
```

## Security Best Practices

1. **Certificate Validation**:
   - Always validate server certificates
   - Use domain matching when possible
   - Keep CA certificates up to date

2. **Credential Management**:
   - Use device-specific credentials when possible
   - Implement certificate-based authentication for managed devices
   - Regularly rotate shared secrets

3. **Network Segmentation**:
   - Use VLANs to segment different user types
   - Implement appropriate firewall rules
   - Monitor network access patterns

4. **Device Management**:
   - Use MDM solutions for mobile devices
   - Deploy configuration profiles centrally
   - Monitor device compliance
