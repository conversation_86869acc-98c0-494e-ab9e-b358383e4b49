# Google Workspace Setup Guide

This guide walks you through setting up Google Workspace for use with the FreeRADIUS Google SSO module.

## Prerequisites

- Google Workspace (formerly G Suite) administrator access
- Google Cloud Platform project (can be created during setup)
- Domain verification in Google Workspace

## Step 1: Create a Google Cloud Project

1. **Go to Google Cloud Console**:
   - Visit [https://console.cloud.google.com/](https://console.cloud.google.com/)
   - Sign in with your Google Workspace admin account

2. **Create a New Project**:
   - Click "Select a project" at the top
   - Click "New Project"
   - Enter project name: `freeradius-sso`
   - Select your organization
   - Click "Create"

3. **Enable Required APIs**:
   - Go to "APIs & Services" > "Library"
   - Search for and enable:
     - Admin SDK API
     - Google Workspace Admin SDK
     - Identity and Access Management (IAM) API

## Step 2: Create a Service Account

1. **Navigate to Service Accounts**:
   - Go to "IAM & Admin" > "Service Accounts"
   - Click "Create Service Account"

2. **Configure Service Account**:
   - Service account name: `freeradius-auth`
   - Service account ID: `freeradius-auth` (auto-generated)
   - Description: `Service account for FreeRADIUS Google SSO authentication`
   - Click "Create and Continue"

3. **Grant Roles** (Optional for now):
   - Skip role assignment for now
   - Click "Continue" then "Done"

4. **Create Service Account Key**:
   - Click on the created service account
   - Go to "Keys" tab
   - Click "Add Key" > "Create new key"
   - Select "JSON" format
   - Click "Create"
   - **Important**: Save the downloaded JSON file securely

## Step 3: Enable Domain-Wide Delegation

1. **Configure Service Account**:
   - In the service account details, check "Enable Google Workspace Domain-wide Delegation"
   - Product name for consent screen: `FreeRADIUS SSO`
   - Click "Save"

2. **Note the Client ID**:
   - Copy the "Client ID" from the service account details
   - You'll need this for the next step

## Step 4: Configure Domain-Wide Delegation in Google Workspace

1. **Access Google Admin Console**:
   - Go to [https://admin.google.com/](https://admin.google.com/)
   - Sign in with your Google Workspace admin account

2. **Navigate to API Controls**:
   - Go to "Security" > "API Controls"
   - Click "Domain-wide Delegation"

3. **Add API Client**:
   - Click "Add new"
   - Client ID: Enter the Client ID from Step 3
   - OAuth Scopes: Enter the following scopes (comma-separated):
     ```
     https://www.googleapis.com/auth/admin.directory.user.readonly,
     https://www.googleapis.com/auth/admin.directory.group.readonly,
     https://www.googleapis.com/auth/admin.directory.orgunit.readonly
     ```
   - Click "Authorize"

## Step 5: Configure User Groups (Optional)

If you want to use group-based access control:

1. **Create WiFi Access Groups**:
   - In Google Admin Console, go to "Directory" > "Groups"
   - Create groups such as:
     - `<EMAIL>` - General WiFi access
     - `<EMAIL>` - Staff WiFi access
     - `<EMAIL>` - Guest WiFi access

2. **Add Users to Groups**:
   - Click on each group
   - Add appropriate users
   - Set group permissions as needed

## Step 6: Test API Access

You can test the API access using the Google APIs Explorer:

1. **Go to APIs Explorer**:
   - Visit [https://developers.google.com/admin-sdk/directory/v1/reference/users/list](https://developers.google.com/admin-sdk/directory/v1/reference/users/list)

2. **Test User Listing**:
   - Click "Try this API"
   - Enter your domain in the "domain" field
   - Click "Execute"
   - You should see a list of users in your domain

## Step 7: Configure FreeRADIUS Module

1. **Place Service Account Key**:
   ```bash
   sudo cp /path/to/downloaded-key.json /etc/freeradius/certs/google-service-account.json
   sudo chown freerad:freerad /etc/freeradius/certs/google-service-account.json
   sudo chmod 600 /etc/freeradius/certs/google-service-account.json
   ```

2. **Update Module Configuration**:
   Edit `/etc/freeradius/mods-enabled/google_sso`:
   ```
   google_sso {
       service_account_email = "<EMAIL>"
       service_account_key = "/etc/freeradius/certs/google-service-account.json"
       domain = "yourdomain.com"
       admin_email = "<EMAIL>"
       
       # Enable group-based access control
       require_group_membership = yes
       required_groups = "wifi-users,wifi-staff"
   }
   ```

## Step 8: Security Considerations

1. **Service Account Key Security**:
   - Store the key file securely with restricted permissions
   - Consider key rotation policies
   - Monitor service account usage

2. **API Scope Limitations**:
   - Only grant minimum required scopes
   - Regularly review and audit API access
   - Monitor API usage in Google Cloud Console

3. **User Access Control**:
   - Use group-based access control
   - Implement appropriate VLAN segmentation
   - Monitor authentication logs

## Step 9: Troubleshooting

### Common Issues

1. **"Access denied" errors**:
   - Verify domain-wide delegation is properly configured
   - Check that all required scopes are granted
   - Ensure the service account email is correct

2. **"Invalid credentials" errors**:
   - Verify the service account key file is valid JSON
   - Check file permissions and ownership
   - Ensure the key hasn't been revoked

3. **"User not found" errors**:
   - Verify the user exists in Google Workspace
   - Check that the user is not suspended
   - Ensure the domain name is correct

### Testing Commands

```bash
# Test FreeRADIUS configuration
sudo freeradius -X

# Test authentication
radtest <EMAIL> password localhost 0 testing123

# Check module loading
sudo freeradius -X 2>&1 | grep google_sso
```

### API Testing with curl

You can test the Google API directly:

```bash
# Get access token (simplified - real implementation uses JWT)
ACCESS_TOKEN="your-access-token"

# Test user lookup
curl -H "Authorization: Bearer $ACCESS_TOKEN" \
     "https://www.googleapis.com/admin/directory/v1/users/<EMAIL>"

# Test group membership
curl -H "Authorization: Bearer $ACCESS_TOKEN" \
     "https://www.googleapis.com/admin/directory/v1/groups?userKey=<EMAIL>"
```

## Step 10: Monitoring and Maintenance

1. **Monitor API Usage**:
   - Check Google Cloud Console for API quotas and usage
   - Set up alerts for unusual activity
   - Monitor authentication success/failure rates

2. **Regular Maintenance**:
   - Rotate service account keys periodically
   - Review and update group memberships
   - Update API scopes as needed

3. **Backup and Recovery**:
   - Backup FreeRADIUS configuration
   - Document service account setup
   - Maintain emergency access procedures

## Additional Resources

- [Google Workspace Admin SDK Documentation](https://developers.google.com/admin-sdk)
- [Service Account Authentication](https://cloud.google.com/docs/authentication/production)
- [Domain-wide Delegation](https://developers.google.com/identity/protocols/oauth2/service-account#delegatingauthority)
- [Google Workspace Security Best Practices](https://support.google.com/a/answer/2520500)

## Support

If you encounter issues:

1. Check the Google Cloud Console for API errors
2. Review FreeRADIUS debug logs (`sudo freeradius -X`)
3. Verify Google Workspace admin console settings
4. Test API access independently of FreeRADIUS
