# 🚀 FreeRADIUS Google SSO Deployment Checklist

Use this checklist to ensure a smooth deployment of your FreeRADIUS Google SSO module.

## Pre-Deployment Preparation

### ✅ **Development Environment (macOS)**
- [ ] Code is complete and tested locally
- [ ] Deployment package created: `freeradius-google-sso-deployment.tar.gz`
- [ ] Google Workspace admin access confirmed
- [ ] Target server access confirmed

### ✅ **Target Server Requirements**
- [ ] Ubuntu 20.04+ or Debian 11+ server available
- [ ] Minimum 2GB RAM, 4GB+ recommended
- [ ] Root/sudo access to server
- [ ] Internet connectivity for Google API calls
- [ ] Ports 1812/1813 available for RADIUS

## Google Workspace Setup

### ✅ **Google Cloud Console**
- [ ] Google Cloud project created/selected
- [ ] Admin SDK API enabled
- [ ] Google Workspace Admin SDK enabled
- [ ] Service account created: `freeradius-auth`
- [ ] Service account JSON key downloaded
- [ ] Domain-wide delegation enabled for service account

### ✅ **Google Admin Console**
- [ ] Domain-wide delegation configured
- [ ] Client ID added with required scopes:
  - `https://www.googleapis.com/auth/admin.directory.user.readonly`
  - `https://www.googleapis.com/auth/admin.directory.group.readonly`
- [ ] WiFi user groups created (optional):
  - `<EMAIL>`
  - `<EMAIL>`
  - `<EMAIL>`
- [ ] Users added to appropriate groups

## Server Deployment

### ✅ **File Transfer**
- [ ] Deployment package transferred to server
- [ ] Package extracted in `/tmp/freeradius-google-sso`
- [ ] Service account JSON key transferred securely

### ✅ **Installation**
- [ ] Dependencies installed: `sudo ./setup.sh dependencies`
- [ ] Module built successfully: `./setup.sh build`
- [ ] Module installed: `sudo ./setup.sh install`
- [ ] Installation tests passed: `./test_module.sh`

### ✅ **Configuration**
- [ ] Service account key placed in `/etc/freeradius/certs/`
- [ ] Key permissions set: `600 freerad:freerad`
- [ ] Module configuration updated in `/etc/freeradius/mods-enabled/google_sso`:
  - [ ] `service_account_email` set
  - [ ] `service_account_key` path set
  - [ ] `domain` set to your Google Workspace domain
  - [ ] `admin_email` set
  - [ ] Group requirements configured (if needed)

### ✅ **FreeRADIUS Configuration**
- [ ] Dictionary updated with Google SSO attributes
- [ ] `default` site updated:
  - [ ] `google_sso` added to `authorize` section
  - [ ] `Auth-Type google_sso` added to `authenticate` section
  - [ ] `google_sso` added to `post-auth` section
- [ ] `inner-tunnel` site updated (for EAP):
  - [ ] `google_sso` added to `authorize` section
  - [ ] `Auth-Type google_sso` added to `authenticate` section

## Testing

### ✅ **Configuration Tests**
- [ ] FreeRADIUS configuration syntax valid: `sudo freeradius -C`
- [ ] Module loads without errors: `sudo freeradius -X | grep google_sso`
- [ ] Google API connectivity working: `./test_module.sh google-api`
- [ ] All tests passing: `./test_module.sh all`

### ✅ **Authentication Tests**
- [ ] Test user authentication: `radtest <EMAIL> password localhost 0 testing123`
- [ ] EAP-TTLS test (if eapol_test available)
- [ ] Group membership validation working
- [ ] RADIUS attributes returned correctly

## WiFi Infrastructure

### ✅ **Access Point Configuration**
- [ ] RADIUS server IP configured
- [ ] RADIUS shared secret configured (`testing123` for testing)
- [ ] Auth port 1812 configured
- [ ] Accounting port 1813 configured
- [ ] WiFi network security set to WPA2-Enterprise
- [ ] EAP method set to TTLS (if configurable)

### ✅ **Network Security**
- [ ] Firewall rules configured:
  - [ ] Port 1812/udp (RADIUS auth)
  - [ ] Port 1813/udp (RADIUS accounting)
- [ ] VLAN configuration (if using group-based VLANs)
- [ ] Network segmentation configured

## Client Testing

### ✅ **Test Devices**
- [ ] Windows client configured and tested
- [ ] macOS client configured and tested
- [ ] Android client configured and tested
- [ ] iOS client configured and tested
- [ ] All clients can authenticate successfully

### ✅ **Client Configuration Verified**
- [ ] Security: WPA2-Enterprise
- [ ] EAP Method: TTLS
- [ ] Phase 2 Authentication: PAP
- [ ] Username format: <EMAIL>
- [ ] Password: Google Workspace password

## Production Hardening

### ✅ **Security**
- [ ] Debug mode disabled in module configuration
- [ ] File permissions secured
- [ ] Service account key permissions verified (600)
- [ ] Firewall configured and enabled
- [ ] RADIUS shared secret changed from default

### ✅ **Monitoring**
- [ ] FreeRADIUS service enabled: `sudo systemctl enable freeradius`
- [ ] Logging configured
- [ ] Log rotation configured
- [ ] Monitoring/alerting set up (optional)

### ✅ **Backup**
- [ ] FreeRADIUS configuration backed up
- [ ] Service account key backed up securely
- [ ] Documentation updated with deployment details

## Post-Deployment

### ✅ **User Communication**
- [ ] WiFi connection instructions provided to users
- [ ] Help desk informed about new authentication method
- [ ] User training conducted (if needed)

### ✅ **Monitoring**
- [ ] Authentication success/failure rates monitored
- [ ] Google API usage monitored
- [ ] System performance monitored
- [ ] Error logs reviewed regularly

### ✅ **Maintenance**
- [ ] Service account key rotation schedule planned
- [ ] Regular security reviews scheduled
- [ ] Update procedures documented

## Troubleshooting Quick Reference

### Common Commands
```bash
# Check FreeRADIUS status
sudo systemctl status freeradius

# Debug mode
sudo freeradius -X

# Test authentication
radtest <EMAIL> password localhost 0 testing123

# Check module loading
./test_module.sh all

# View logs
sudo tail -f /var/log/freeradius/radius.log
```

### Emergency Contacts
- [ ] Google Workspace admin contact: ________________
- [ ] Network infrastructure admin: ________________
- [ ] FreeRADIUS administrator: ________________

## Sign-off

### ✅ **Deployment Approval**
- [ ] Technical testing completed successfully
- [ ] Security review completed
- [ ] Documentation updated
- [ ] Stakeholders notified

**Deployed by**: ________________  
**Date**: ________________  
**Environment**: ________________  

---

## Notes
Use this space for deployment-specific notes, issues encountered, or special configurations:

```
[Add your deployment notes here]
```
