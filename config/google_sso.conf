#
# google_sso.conf - Configuration template for rlm_google_sso
#
# This file should be placed in /etc/freeradius/mods-available/google_sso
# and linked to /etc/freeradius/mods-enabled/google_sso
#

google_sso {
    #
    # Google Workspace Configuration
    #
    
    # Service account email for API access
    # This service account must have domain-wide delegation enabled
    service_account_email = "<EMAIL>"
    
    # Path to the service account private key file (JSON format)
    service_account_key = "/etc/freeradius/certs/google-service-account.json"
    
    # Your Google Workspace domain
    domain = "example.com"
    
    # Admin email for domain-wide delegation
    admin_email = "<EMAIL>"
    
    #
    # API Configuration
    #
    
    # Google API base URL (usually doesn't need to be changed)
    api_base_url = "https://www.googleapis.com"
    
    # API request timeout in seconds
    api_timeout = 30
    
    # Access token cache TTL in seconds
    token_cache_ttl = 3600
    
    #
    # Authentication Configuration
    #
    
    # Enable certificate-based authentication (EAP-TLS)
    enable_certificate_auth = yes
    
    # Enable password fallback authentication
    enable_password_fallback = yes
    
    # Certificate template for user certificates
    certificate_template = "user"
    
    # Certificate validity period in days
    certificate_validity_days = 365
    
    #
    # Security Configuration
    #
    
    # Maximum authentication attempts before lockout
    max_auth_attempts = 5
    
    # Account lockout duration in seconds (15 minutes)
    lockout_duration = 900
    
    # Require specific group membership for access
    require_group_membership = no
    
    # Comma-separated list of required groups (if require_group_membership = yes)
    # required_groups = "wifi-users,employees"
    
    #
    # Caching Configuration
    #
    
    # User information cache TTL in seconds
    user_cache_ttl = 300
    
    # Group membership cache TTL in seconds
    group_cache_ttl = 600
    
    # Enable offline authentication using cached credentials
    enable_offline_auth = yes
    
    #
    # Logging Configuration
    #
    
    # Enable debug mode (verbose logging)
    debug_mode = no
    
    # Optional log file path (if not specified, uses syslog)
    # log_file = "/var/log/freeradius/google_sso.log"
}

#
# Example policy configuration for different user groups
#
# This would typically go in /etc/freeradius/policy.d/google_sso_policy
#
# google_sso_policy {
#     # Default VLAN for authenticated users
#     if (&Google-User-Groups && (&Google-User-Groups[*] == "staff")) {
#         update reply {
#             Tunnel-Type := VLAN
#             Tunnel-Medium-Type := IEEE-802
#             Tunnel-Private-Group-ID := 100
#         }
#     }
#     elsif (&Google-User-Groups && (&Google-User-Groups[*] == "students")) {
#         update reply {
#             Tunnel-Type := VLAN
#             Tunnel-Medium-Type := IEEE-802
#             Tunnel-Private-Group-ID := 200
#         }
#     }
#     elsif (&Google-User-Groups && (&Google-User-Groups[*] == "guests")) {
#         update reply {
#             Tunnel-Type := VLAN
#             Tunnel-Medium-Type := IEEE-802
#             Tunnel-Private-Group-ID := 300
#             Session-Timeout := 3600
#         }
#     }
#     else {
#         # Default VLAN for other users
#         update reply {
#             Tunnel-Type := VLAN
#             Tunnel-Medium-Type := IEEE-802
#             Tunnel-Private-Group-ID := 999
#         }
#     }
# }
